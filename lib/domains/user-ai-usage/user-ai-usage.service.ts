import { db } from "@/lib/firebase"
import { doc, getDoc, updateDoc, increment, serverTimestamp, setDoc } from "firebase/firestore"
import { ServiceResponse } from "../base/base.types"
import { AI_USAGE_LIMITS, AIUsageSummary, AIUsageCategory } from "./user-ai-usage.types"

/**
 * User AI usage service for Firebase operations
 */
export class UserAIUsageService {
  private static readonly COLLECTION = "userAiUsage"

  /**
   * Check if a user can make an AI request
   * @param userId User ID
   * @param hasSubscription Whether the user has an active subscription
   * @param category Optional category to check limits for
   * @returns Whether the user can make an AI request
   */
  static async canMakeAIRequest(
    userId: string,
    hasSubscription: boolean,
    category?: AIUsageCategory
  ): Promise<boolean> {
    try {
      // If user has an active subscription, they can make unlimited requests
      if (hasSubscription) return true

      // Get user's AI usage
      const userAIUsageDoc = await getDoc(doc(db, this.COLLECTION, userId))

      // If no AI usage document exists, user hasn't used AI yet
      if (!userAIUsageDoc.exists()) {
        return true
      }

      const userAIUsageData = userAIUsageDoc.data()

      // If a specific category is provided, check category-specific limits
      if (category && userAIUsageData.categoryUsage && userAIUsageData.categoryUsage[category]) {
        const categoryData = userAIUsageData.categoryUsage[category]
        const categoryLimit = AI_USAGE_LIMITS.FREE.CATEGORIES[category]

        // Check if category limit is reached
        if (categoryData.count && categoryData.count >= categoryLimit) {
          return false
        }
      }

      // If no category specified or category data doesn't exist yet, fall back to legacy checks

      // Check daily limit
      // const now = new Date()
      // const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()

      // // If aiUsageLastReset is not set or is from a previous day, the user hasn't used AI today
      // if (
      //   !userAIUsageData.aiUsageLastReset ||
      //   (userAIUsageData.aiUsageLastReset.toMillis &&
      //     userAIUsageData.aiUsageLastReset.toMillis() < today)
      // ) {
      //   return true
      // }

      // // Check if daily limit is reached
      // if (
      //   userAIUsageData.aiUsageToday &&
      //   userAIUsageData.aiUsageToday >= AI_USAGE_LIMITS.FREE.DAILY
      // ) {
      //   return false
      // }

      // // Check weekly limit
      // const oneWeekAgo = new Date()
      // oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      // const weekStart = oneWeekAgo.getTime()

      // // If aiUsageWeekStart is not set or is from more than a week ago, reset the weekly counter
      // if (
      //   !userAIUsageData.aiUsageWeekStart ||
      //   (userAIUsageData.aiUsageWeekStart.toMillis &&
      //     userAIUsageData.aiUsageWeekStart.toMillis() < weekStart)
      // ) {
      //   return true
      // }

      // // Check if weekly limit is reached
      // if (
      //   userAIUsageData.aiUsageThisWeek &&
      //   userAIUsageData.aiUsageThisWeek >= AI_USAGE_LIMITS.FREE.WEEKLY
      // ) {
      //   return false
      // }

      return true
    } catch (error) {
      console.error("Error checking if user can make AI request:", error)
      return false
    }
  }

  /**
   * Increment AI usage counter for a user
   * @param userId User ID
   * @param category Optional category to increment usage for
   * @returns Service response indicating success or failure
   */
  static async incrementAIUsage(
    userId: string,
    category?: AIUsageCategory
  ): Promise<ServiceResponse> {
    try {
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      const weekStart = oneWeekAgo.getTime()

      // Get current usage data
      const userAIUsageDoc = await getDoc(doc(db, this.COLLECTION, userId))

      if (!userAIUsageDoc.exists()) {
        // Create new document with initial values
        const initialData: any = {
          userId,
          aiUsageToday: 1,
          aiUsageThisWeek: 1,
          aiUsageLastReset: serverTimestamp(),
          aiUsageWeekStart: serverTimestamp(),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          // Initialize total usage tracking
          totalUsage: {
            count: 1,
            lastReset: serverTimestamp(),
            daily: {
              count: 1,
              lastReset: serverTimestamp(),
            },
            weekly: {
              count: 1,
              lastReset: serverTimestamp(),
            },
          },
        }

        // Add category-specific data if a category is provided
        if (category) {
          initialData.categoryUsage = {
            [category]: {
              count: 1,
              lastReset: serverTimestamp(),
            },
          }
        }

        await setDoc(doc(db, this.COLLECTION, userId), initialData)
      } else {
        const userAIUsageData = userAIUsageDoc.data()

        // Always increment the total usage counter
        if (!userAIUsageData.totalUsage) {
          // Initialize total usage if it doesn't exist
          await updateDoc(doc(db, this.COLLECTION, userId), {
            totalUsage: {
              count: 1,
              lastReset: serverTimestamp(),
              daily: {
                count: 1,
                lastReset: serverTimestamp(),
              },
              weekly: {
                count: 1,
                lastReset: serverTimestamp(),
              },
            },
            updatedAt: serverTimestamp(),
          })
        } else {
          // Increment the total usage counter
          await updateDoc(doc(db, this.COLLECTION, userId), {
            "totalUsage.count": increment(1),
            updatedAt: serverTimestamp(),
          })

          // Check if we need to reset daily total counter
          if (
            !userAIUsageData.totalUsage.daily?.lastReset ||
            (userAIUsageData.totalUsage.daily?.lastReset.toMillis &&
              userAIUsageData.totalUsage.daily.lastReset.toMillis() < today)
          ) {
            await updateDoc(doc(db, this.COLLECTION, userId), {
              "totalUsage.daily.count": 1,
              "totalUsage.daily.lastReset": serverTimestamp(),
              updatedAt: serverTimestamp(),
            })
          } else {
            // Increment the daily total counter
            await updateDoc(doc(db, this.COLLECTION, userId), {
              "totalUsage.daily.count": increment(1),
              updatedAt: serverTimestamp(),
            })
          }

          // Check if we need to reset weekly total counter
          if (
            !userAIUsageData.totalUsage.weekly?.lastReset ||
            (userAIUsageData.totalUsage.weekly?.lastReset.toMillis &&
              userAIUsageData.totalUsage.weekly.lastReset.toMillis() < weekStart)
          ) {
            await updateDoc(doc(db, this.COLLECTION, userId), {
              "totalUsage.weekly.count": 1,
              "totalUsage.weekly.lastReset": serverTimestamp(),
              updatedAt: serverTimestamp(),
            })
          } else {
            // Increment the weekly total counter
            await updateDoc(doc(db, this.COLLECTION, userId), {
              "totalUsage.weekly.count": increment(1),
              updatedAt: serverTimestamp(),
            })
          }
        }

        // Handle category-specific updates if a category is provided
        if (category) {
          // Check if categoryUsage exists
          if (!userAIUsageData.categoryUsage || !userAIUsageData.categoryUsage[category]) {
            // Create or update the category usage
            await updateDoc(doc(db, this.COLLECTION, userId), {
              [`categoryUsage.${category}`]: {
                count: 1,
                lastReset: serverTimestamp(),
              },
              updatedAt: serverTimestamp(),
            })
          } else {
            // Increment the category counter
            await updateDoc(doc(db, this.COLLECTION, userId), {
              [`categoryUsage.${category}.count`]: increment(1),
              updatedAt: serverTimestamp(),
            })
          }
        }

        // Continue with legacy updates for backward compatibility

        // Check if we need to reset daily counter
        if (
          !userAIUsageData.aiUsageLastReset ||
          (userAIUsageData.aiUsageLastReset.toMillis &&
            userAIUsageData.aiUsageLastReset.toMillis() < today)
        ) {
          await updateDoc(doc(db, this.COLLECTION, userId), {
            aiUsageToday: 1,
            aiUsageLastReset: serverTimestamp(),
            updatedAt: serverTimestamp(),
          })
        } else {
          // Increment the daily counter
          await updateDoc(doc(db, this.COLLECTION, userId), {
            aiUsageToday: increment(1),
            updatedAt: serverTimestamp(),
          })
        }

        // Check if we need to reset weekly counter
        if (
          !userAIUsageData.aiUsageWeekStart ||
          (userAIUsageData.aiUsageWeekStart.toMillis &&
            userAIUsageData.aiUsageWeekStart.toMillis() < weekStart)
        ) {
          await updateDoc(doc(db, this.COLLECTION, userId), {
            aiUsageThisWeek: 1,
            aiUsageWeekStart: serverTimestamp(),
            updatedAt: serverTimestamp(),
          })
        } else {
          // Increment the weekly counter
          await updateDoc(doc(db, this.COLLECTION, userId), {
            aiUsageThisWeek: increment(1),
            updatedAt: serverTimestamp(),
          })
        }
      }

      return { success: true }
    } catch (error) {
      console.error("Error incrementing AI usage:", error)
      return { success: false, error }
    }
  }

  /**
   * Get current AI usage for a user
   * @param userId User ID
   * @param hasSubscription Whether the user has an active subscription
   * @returns AI usage summary
   */
  static async getAIUsage(userId: string, hasSubscription: boolean): Promise<AIUsageSummary> {
    try {
      // Determine limits based on subscription status
      const dailyLimit = hasSubscription ? AI_USAGE_LIMITS.PRO.DAILY : AI_USAGE_LIMITS.FREE.DAILY
      const weeklyLimit = hasSubscription ? AI_USAGE_LIMITS.PRO.WEEKLY : AI_USAGE_LIMITS.FREE.WEEKLY

      // Get category limits
      const categoryLimits = hasSubscription
        ? AI_USAGE_LIMITS.PRO.CATEGORIES
        : AI_USAGE_LIMITS.FREE.CATEGORIES

      // Get user's AI usage document
      const userAIUsageDoc = await getDoc(doc(db, this.COLLECTION, userId))

      // If no AI usage document exists, return default values
      if (!userAIUsageDoc.exists()) {
        return {
          daily: 0,
          weekly: 0,
          dailyLimit,
          weeklyLimit,
          canMakeRequest: true,
          // Include default total usage data
          totalUsage: {
            count: 0,
            daily: {
              count: 0,
              limit: dailyLimit,
            },
            weekly: {
              count: 0,
              limit: weeklyLimit,
            },
          },
          categories: {
            [AIUsageCategory.TRIP]: {
              count: 0,
              limit: categoryLimits[AIUsageCategory.TRIP],
              canMakeRequest: true,
            },
            [AIUsageCategory.TASK]: {
              count: 0,
              limit: categoryLimits[AIUsageCategory.TASK],
              canMakeRequest: true,
            },
            [AIUsageCategory.ITINERARY]: {
              count: 0,
              limit: categoryLimits[AIUsageCategory.ITINERARY],
              canMakeRequest: true,
            },
          },
        }
      }

      const userAIUsageData = userAIUsageDoc.data()
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()
      let daily = userAIUsageData.aiUsageToday || 0

      // Reset daily counter if it's a new day
      if (
        !userAIUsageData.aiUsageLastReset ||
        (userAIUsageData.aiUsageLastReset.toMillis &&
          userAIUsageData.aiUsageLastReset.toMillis() < today)
      ) {
        daily = 0
        // Update the database to reset the counter
        await updateDoc(doc(db, this.COLLECTION, userId), {
          aiUsageToday: 0,
          aiUsageLastReset: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })
      }

      // Check weekly counter
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      const weekStart = oneWeekAgo.getTime()
      let weekly = userAIUsageData.aiUsageThisWeek || 0

      // Reset weekly counter if it's been more than a week
      if (
        !userAIUsageData.aiUsageWeekStart ||
        (userAIUsageData.aiUsageWeekStart.toMillis &&
          userAIUsageData.aiUsageWeekStart.toMillis() < weekStart)
      ) {
        weekly = 0
        // Update the database to reset the counter
        await updateDoc(doc(db, this.COLLECTION, userId), {
          aiUsageThisWeek: 0,
          aiUsageWeekStart: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })
      }

      // Determine if the user can make a request (legacy check)
      const canMakeRequest = hasSubscription || (daily < dailyLimit && weekly < weeklyLimit)

      // Process category-specific usage data
      const categories: AIUsageSummary["categories"] = {}

      // Initialize with default values for all categories
      Object.values(AIUsageCategory).forEach((category) => {
        categories[category] = {
          count: 0,
          limit: categoryLimits[category],
          canMakeRequest: hasSubscription || 0 < categoryLimits[category], // Pro users can always make requests (Infinity limit)
        }
      })

      // Update with actual values if they exist
      if (userAIUsageData.categoryUsage) {
        Object.entries(userAIUsageData.categoryUsage).forEach(([category, data]) => {
          if (Object.values(AIUsageCategory).includes(category as AIUsageCategory)) {
            const typedCategory = category as AIUsageCategory
            const count =
              typeof data === "object" && data !== null && "count" in data
                ? (data as { count?: number }).count || 0
                : 0
            const limit = categoryLimits[typedCategory]

            categories[typedCategory] = {
              count,
              limit,
              canMakeRequest: hasSubscription || count < limit,
            }
          }
        })
      }

      // Process total usage data
      let totalUsage: AIUsageSummary["totalUsage"] = {
        count: 0,
        daily: {
          count: 0,
          limit: dailyLimit,
        },
        weekly: {
          count: 0,
          limit: weeklyLimit,
        },
      }

      // Update with actual values if they exist
      if (userAIUsageData.totalUsage) {
        const totalCount = userAIUsageData.totalUsage.count || 0

        // Process daily total usage
        let dailyTotalCount = 0
        if (userAIUsageData.totalUsage.daily) {
          dailyTotalCount = userAIUsageData.totalUsage.daily.count || 0

          // Reset if needed
          if (
            !userAIUsageData.totalUsage.daily.lastReset ||
            (userAIUsageData.totalUsage.daily.lastReset.toMillis &&
              userAIUsageData.totalUsage.daily.lastReset.toMillis() < today)
          ) {
            dailyTotalCount = 0
            // We don't update the database here as it will be updated on next increment
          }
        }

        // Process weekly total usage
        let weeklyTotalCount = 0
        if (userAIUsageData.totalUsage.weekly) {
          weeklyTotalCount = userAIUsageData.totalUsage.weekly.count || 0

          // Reset if needed
          if (
            !userAIUsageData.totalUsage.weekly.lastReset ||
            (userAIUsageData.totalUsage.weekly.lastReset.toMillis &&
              userAIUsageData.totalUsage.weekly.lastReset.toMillis() < weekStart)
          ) {
            weeklyTotalCount = 0
            // We don't update the database here as it will be updated on next increment
          }
        }

        totalUsage = {
          count: totalCount,
          daily: {
            count: dailyTotalCount,
            limit: dailyLimit,
          },
          weekly: {
            count: weeklyTotalCount,
            limit: weeklyLimit,
          },
        }
      }

      return {
        daily,
        weekly,
        dailyLimit,
        weeklyLimit,
        canMakeRequest,
        totalUsage,
        categories,
      }
    } catch (error) {
      console.error("Error getting AI usage:", error)

      // Get category limits for default response
      const categoryLimits = hasSubscription
        ? AI_USAGE_LIMITS.PRO.CATEGORIES
        : AI_USAGE_LIMITS.FREE.CATEGORIES

      return {
        daily: 0,
        weekly: 0,
        dailyLimit: AI_USAGE_LIMITS.FREE.DAILY,
        weeklyLimit: AI_USAGE_LIMITS.FREE.WEEKLY,
        canMakeRequest: true,
        // Include default total usage data
        totalUsage: {
          count: 0,
          daily: {
            count: 0,
            limit: AI_USAGE_LIMITS.FREE.DAILY,
          },
          weekly: {
            count: 0,
            limit: AI_USAGE_LIMITS.FREE.WEEKLY,
          },
        },
        categories: {
          [AIUsageCategory.TRIP]: {
            count: 0,
            limit: categoryLimits[AIUsageCategory.TRIP],
            canMakeRequest: true,
          },
          [AIUsageCategory.TASK]: {
            count: 0,
            limit: categoryLimits[AIUsageCategory.TASK],
            canMakeRequest: true,
          },
          [AIUsageCategory.ITINERARY]: {
            count: 0,
            limit: categoryLimits[AIUsageCategory.ITINERARY],
            canMakeRequest: true,
          },
        },
      }
    }
  }
}
