"use client"

import { useEffect, useState } from "react"
import { AIUsageSummary, AIUsageCategory } from "./user-ai-usage.types"
import { UserAIUsageRealtimeService } from "./user-ai-usage.realtime.service"
import { useUser } from "../auth/auth.hooks"

/**
 * Hook to get real-time updates for AI usage
 */
export const useRealtimeUserAIUsage = (hasSubscription: boolean | undefined) => {
  const user = useUser()
  const userId = user?.uid || ""

  const [usage, setUsage] = useState<AIUsageSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userId) {
      setUsage(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    // Default to false (free user) if subscription status is undefined
    const subscriptionStatus = hasSubscription ?? false

    const unsubscribe = UserAIUsageRealtimeService.subscribeToUserAIUsage(
      userId,
      subscriptionStatus,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time AI usage:", err)
          setError(err)
          setLoading(false)
          return
        }

        setUsage(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [userId, hasSubscription])

  // Get category-specific usage data
  const getCategoryUsage = (category: AIUsageCategory) => {
    if (!usage || !usage.categories) return null
    return usage.categories[category]
  }

  return {
    usage,
    loading,
    error,
    getCategoryUsage,
  }
}
