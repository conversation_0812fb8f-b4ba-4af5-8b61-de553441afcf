"use client"

import { useEffect, useState } from "react"
import { useUserTripStore } from "./user-trip.store"
import { UserTripStatus } from "./user-trip.types"
import { UserTripService } from "./user-trip.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"

// Export real-time hooks
export * from "./user-trip.realtime.hooks"
import {
  useRealtimeUserTripStatus,
  useRealtimeTripAttendees,
  useRealtimeUserAttendingTrips,
} from "./user-trip.realtime.hooks"

/**
 * Hook to check a user's status for a trip
 */
export const useUserTripStatus = (tripId: string) => {
  const user = useUser()
  const { currentUserTrip, loading, error, checkUserTripStatus } = useUserTripStore()
  const [status, setStatus] = useState<UserTripStatus | null>(null)

  useEffect(() => {
    const fetchStatus = async () => {
      if (user?.uid && tripId) {
        const userTrip = await checkUserTripStatus(user.uid, tripId)
        if (userTrip) {
          setStatus(userTrip.status)
        }
      }
    }

    fetchStatus()

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user, tripId, checkUserTripStatus])

  return { status, userTrip: currentUserTrip, loading, error }
}

/**
 * Hook to update a user's trip status
 */
export const useUpdateUserTripStatus = (tripId: string) => {
  const user = useUser()
  const { loading, error, updateUserTripStatus } = useUserTripStore()
  const [updating, setUpdating] = useState(false)
  const [updateError, setUpdateError] = useState<Error | null>(null)

  const updateStatus = async (status: UserTripStatus) => {
    if (!user?.uid) {
      setUpdateError(new Error("User not authenticated"))
      return false
    }

    try {
      setUpdating(true)
      setUpdateError(null)
      const success = await updateUserTripStatus(user.uid, tripId, status)
      setUpdating(false)
      return success
    } catch (err) {
      setUpdateError(err as Error)
      setUpdating(false)
      return false
    }
  }

  return { updateStatus, updating: updating || loading, error: updateError || error }
}

/**
 * Hook to get trip attendees
 */
export const useTripAttendees = (tripId: string, useRealtime: boolean = false) => {
  // If useRealtime is true, use the real-time hook instead
  if (useRealtime) {
    return useRealtimeTripAttendees(tripId)
  }

  // Otherwise use the regular store
  const { loading, error, getTripAttendees } = useUserTripStore()
  const [attendees, setAttendees] = useState<string[]>([])
  const [fetchingAttendees, setFetchingAttendees] = useState(false)

  useEffect(() => {
    const fetchAttendees = async () => {
      if (tripId) {
        setFetchingAttendees(true)
        const userIds = await getTripAttendees(tripId)
        setAttendees(userIds)
        setFetchingAttendees(false)
      }
    }

    fetchAttendees()

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [tripId, getTripAttendees])

  return { attendees, loading: fetchingAttendees || loading, error }
}

/**
 * Hook to get trip IDs a user is attending
 */
export const useUserAttendingTripIds = (useRealtime: boolean = false) => {
  // If useRealtime is true, use the real-time hook instead
  if (useRealtime) {
    return useRealtimeUserAttendingTrips()
  }

  // Otherwise use the regular store
  const user = useUser()
  const { loading, error, getUserAttendingTrips } = useUserTripStore()
  const [tripIds, setTripIds] = useState<string[]>([])
  const [fetchingTrips, setFetchingTrips] = useState(false)

  useEffect(() => {
    const fetchTrips = async () => {
      if (user?.uid) {
        setFetchingTrips(true)
        const ids = await getUserAttendingTrips(user.uid)
        setTripIds(ids)
        setFetchingTrips(false)
      }
    }

    fetchTrips()

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user, getUserAttendingTrips])

  return { tripIds, loading: fetchingTrips || loading, error }
}

/**
 * Hook to get trip IDs a user is interested in
 */
export const useUserInterestedTripIds = () => {
  const user = useUser()
  const [tripIds, setTripIds] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTrips = async () => {
      if (user?.uid) {
        setLoading(true)
        setError(null)
        try {
          const ids = await UserTripService.getUserInterestedTrips(user.uid)
          setTripIds(ids)
        } catch (err) {
          setError(err as Error)
        } finally {
          setLoading(false)
        }
      }
    }

    fetchTrips()

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user])

  return { tripIds, loading, error }
}

/**
 * Hook to get trips by user status
 */
export const useUserTripsByStatus = (status: UserTripStatus) => {
  const user = useUser()
  const [tripIds, setTripIds] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTrips = async () => {
      if (user?.uid) {
        setLoading(true)
        setError(null)
        try {
          const ids = await UserTripService.getUserTripsByStatus(user.uid, status)
          setTripIds(ids)
        } catch (err) {
          setError(err as Error)
        } finally {
          setLoading(false)
        }
      }
    }

    fetchTrips()

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [user, status])

  return { tripIds, loading, error }
}

/**
 * Hook to manually sync trip attendees
 */
export const useSyncTripAttendees = () => {
  const [syncing, setSyncing] = useState(false)
  const [syncError, setSyncError] = useState<Error | null>(null)

  const syncAttendees = async (tripId: string) => {
    try {
      setSyncing(true)
      setSyncError(null)
      const success = await UserTripService.syncTripAttendees(tripId)
      setSyncing(false)
      return success
    } catch (err) {
      setSyncError(err as Error)
      setSyncing(false)
      return false
    }
  }

  return { syncAttendees, syncing, error: syncError }
}
