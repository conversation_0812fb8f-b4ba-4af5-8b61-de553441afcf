"use client"

import { useState, useEffect, useMemo } from "react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useToast } from "@/components/ui/use-toast"
import { InvitationService } from "./invitation.service"
import { InvitationSendService } from "./invitation-send.service"
import { SquadService } from "../squad/squad.service"
import { InvitationLink, InvitationSend, InvitationSendStatus } from "./invitation.types"

export interface SquadInvitationData {
  invitationLink: InvitationLink | null
  specificInvitee: InvitationSend | null
  isAlreadyMember: boolean
  invitationStatus: InvitationSendStatus | null
  isExpired: boolean
}

export interface SquadInvitationActions {
  joinSquad: () => Promise<boolean>
  declineInvitation: () => Promise<boolean>
  refetch: () => Promise<void>
}

export interface SquadInvitationState {
  loading: boolean
  processing: boolean
  declining: boolean
  error: string | null
}

/**
 * Hook to manage squad invitation functionality
 * Handles fetching invitation data, checking user status, and processing actions
 */
export const useSquadInvitation = (invitationId: string, invitationSendId?: string) => {
  const user = useUser()
  const { toast } = useToast()

  // State
  const [data, setData] = useState<SquadInvitationData>({
    invitationLink: null,
    specificInvitee: null,
    isAlreadyMember: false,
    invitationStatus: null,
    isExpired: false,
  })

  const [state, setState] = useState<SquadInvitationState>({
    loading: true,
    processing: false,
    declining: false,
    error: null,
  })

  // Memoize dependencies to prevent infinite re-renders
  const memoizedDependencies = useMemo(
    () => ({ invitationId, invitationSendId, userId: user?.uid, userEmail: user?.email }),
    [invitationId, invitationSendId, user?.uid, user?.email]
  )

  // Fetch invitation data and check user status
  const fetchInvitationData = async () => {
    if (!memoizedDependencies.invitationId) {
      setState((prev) => ({ ...prev, error: "Invalid invitation link.", loading: false }))
      return
    }

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }))

      // Fetch invitation link
      const invitationLink = await InvitationService.getInvitationLink(
        memoizedDependencies.invitationId
      )

      if (!invitationLink) {
        setState((prev) => ({
          ...prev,
          error: "Invitation not found or has been cancelled.",
          loading: false,
        }))
        return
      }

      // Check if invitation is expired
      const now = new Date()
      const expiresAt = invitationLink.expiresAt.toDate()
      const isExpired = expiresAt <= now || !invitationLink.isActive

      if (isExpired) {
        setData((prev) => ({ ...prev, invitationLink, isExpired: true }))
        setState((prev) => ({ ...prev, error: "This invitation has expired.", loading: false }))
        return
      }

      // Only get invitation sends for specific invitations (when sendId is provided)
      let specificInvitee: InvitationSend | null = null
      if (memoizedDependencies.invitationSendId) {
        const invitationSends = await InvitationSendService.getInvitationSends(
          invitationLink.squadId,
          memoizedDependencies.invitationId
        )
        specificInvitee =
          invitationSends.find((send) => send.id === memoizedDependencies.invitationSendId) || null
      }

      // Check user status if logged in
      let isAlreadyMember = false
      let invitationStatus: InvitationSendStatus | null = null

      if (memoizedDependencies.userId) {
        // Check squad membership
        isAlreadyMember = await SquadService.isUserSquadMember(
          memoizedDependencies.userId,
          invitationLink.squadId
        )

        // Check invitation status ONLY for specific invitations (when sendId is provided)
        if (specificInvitee) {
          invitationStatus = specificInvitee.status
        }
        // For generic invitations (no sendId), we don't check invitation-sends status at all

        // Validate email for specific invitations
        if (specificInvitee && memoizedDependencies.userEmail) {
          const userEmail = memoizedDependencies.userEmail.toLowerCase()
          const inviteeEmail = specificInvitee.email.toLowerCase()

          if (userEmail !== inviteeEmail) {
            setState((prev) => ({
              ...prev,
              error: `This invitation is specifically for ${specificInvitee.email}. You are logged in as ${memoizedDependencies.userEmail}. Please log in with the correct account or contact the squad leader.`,
              loading: false,
            }))
            return
          }
        }
      }

      // Update data
      setData({
        invitationLink,
        specificInvitee,
        isAlreadyMember,
        invitationStatus,
        isExpired,
      })

      setState((prev) => ({ ...prev, loading: false }))
    } catch (error) {
      console.error("Error fetching invitation data:", error)
      setState((prev) => ({
        ...prev,
        error: "There was a problem with this invitation. Please try again later.",
        loading: false,
      }))
    }
  }

  // Join squad action
  const joinSquad = async (): Promise<boolean> => {
    if (!user || !data.invitationLink) {
      toast({
        title: "Error",
        description: "Unable to process invitation. Please try again later.",
        variant: "destructive",
      })
      return false
    }

    try {
      setState((prev) => ({ ...prev, processing: true }))

      // Determine join method
      const joinMethod = data.specificInvitee ? "email_invitation" : "shareable_link"

      // Add user to squad with tracking
      const result = await SquadService.addMemberWithTracking(
        data.invitationLink.squadId,
        user.uid,
        user.email || "",
        user.displayName || "Unknown User",
        joinMethod,
        data.invitationLink.id,
        data.invitationLink.inviterId
      )

      if (result.success) {
        // Update invitation send status if specific invitee
        if (data.specificInvitee) {
          try {
            await InvitationSendService.updateInvitationSendStatus(
              data.invitationLink.squadId,
              data.specificInvitee.id,
              "accepted"
            )
          } catch (error) {
            console.warn("Failed to update invitation send status:", error)
          }
        }

        toast({
          title: "Welcome to the squad!",
          description: `You have successfully joined ${data.invitationLink.squadName}`,
        })

        setState((prev) => ({ ...prev, processing: false }))
        return true
      } else {
        throw new Error("Failed to join squad")
      }
    } catch (error) {
      console.error("Error joining squad:", error)
      toast({
        title: "Error joining squad",
        description: "We couldn't add you to the squad at this time. Please try again later.",
        variant: "destructive",
      })
      setState((prev) => ({ ...prev, processing: false }))
      return false
    }
  }

  // Decline invitation action
  const declineInvitation = async (): Promise<boolean> => {
    if (!user || !data.invitationLink) {
      toast({
        title: "Error",
        description: "You must be logged in to decline this invitation.",
        variant: "destructive",
      })
      return false
    }

    try {
      setState((prev) => ({ ...prev, declining: true }))

      // Update invitation send status if specific invitee
      if (data.specificInvitee) {
        await InvitationSendService.updateInvitationSendStatus(
          data.invitationLink.squadId,
          data.specificInvitee.id,
          "rejected"
        )
      }

      toast({
        title: "Invitation declined",
        description: `You have declined the invitation to join ${data.invitationLink.squadName}.`,
      })

      setState((prev) => ({ ...prev, declining: false }))
      return true
    } catch (error) {
      console.error("Error declining invitation:", error)
      toast({
        title: "Error",
        description: "Failed to decline invitation. Please try again.",
        variant: "destructive",
      })
      setState((prev) => ({ ...prev, declining: false }))
      return false
    }
  }

  // Refetch data
  const refetch = async () => {
    await fetchInvitationData()
  }

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchInvitationData()
  }, [
    memoizedDependencies.invitationId,
    memoizedDependencies.invitationSendId,
    memoizedDependencies.userId,
  ])

  const actions: SquadInvitationActions = {
    joinSquad,
    declineInvitation,
    refetch,
  }

  return {
    data,
    state,
    actions,
  }
}
