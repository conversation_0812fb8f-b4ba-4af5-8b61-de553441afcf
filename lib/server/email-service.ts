import { Invitation } from "../firebase/invitation-service"
import { TransactionalEmailsApi, SendSmtpEmail } from "@getbrevo/brevo"
import { EmailTemplates } from "./email-templates"

// Configure the Brevo API client
const apiKey = process.env.BREVO_API_KEY

// Log warning if API key is not set
if (!apiKey) {
  console.warn("BREVO_API_KEY is not configured. Email sending will not work.")
}

// Initialize the Brevo API client
const apiInstance = new TransactionalEmailsApi()
apiInstance.setApiKey(0, apiKey || "")

/**
 * Send an email using the Brevo API
 */
export const sendEmail = async (options: {
  to: string
  subject?: string
  templateId?: number
  params?: Record<string, any>
  htmlContent?: string
  from?: string
}) => {
  try {
    // Check if Brevo API key is configured
    if (!apiKey) {
      console.error("BREVO_API_KEY is not configured")
      return {
        success: false,
        error: "Email service is not properly configured",
      }
    }

    // Create the send email request
    const sendSmtpEmail = new SendSmtpEmail()

    // Set the sender
    sendSmtpEmail.sender = {
      name: "Togeda.ai",
      email: options.from || process.env.EMAIL_FROM || "<EMAIL>",
    }

    // Set the recipient
    sendSmtpEmail.to = [{ email: options.to }]

    // Set the email content - either template or direct HTML
    if (options.templateId && options.params) {
      sendSmtpEmail.templateId = options.templateId
      sendSmtpEmail.params = options.params
      // Subject is not needed for templates as it's defined in the template
    } else if (options.htmlContent && options.subject) {
      sendSmtpEmail.subject = options.subject
      sendSmtpEmail.htmlContent = options.htmlContent
    } else {
      return {
        success: false,
        error: "Either templateId and params or htmlContent and subject must be provided",
      }
    }

    try {
      // Send the email
      const data = await apiInstance.sendTransacEmail(sendSmtpEmail)

      // Extract messageId from response
      const messageId = data?.body?.messageId || `email-${Date.now()}`

      return {
        success: true,
        messageId,
      }
    } catch (sendError) {
      console.error("Error in Brevo API sendTransacEmail:", sendError)
      return {
        success: false,
        error: sendError instanceof Error ? sendError.message : "Error sending email via Brevo API",
      }
    }
  } catch (error) {
    console.error("Error sending email via Brevo:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }
  }
}
/**
 * Generate an invitation link
 */
export const generateInvitationLink = (invitationId: string, invitationSendId?: string): string => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"

  if (invitationSendId) {
    // For specific email invitations, use the authenticated route with sendId
    return `${baseUrl}/invitation/${invitationId}/${invitationSendId}`
  } else {
    // For general invitation links, use the public route (will redirect to authenticated route after login)
    return `${baseUrl}/invitation/${invitationId}`
  }
}

/**
 * Check if a user exists by email using Firebase Admin
 */
const checkUserExistsByEmail = async (
  email: string
): Promise<{ exists: boolean; userData?: any }> => {
  try {
    const { getAdminInstance } = await import("../firebase-admin")
    const { adminAuth } = await getAdminInstance()

    if (!adminAuth) {
      console.warn("Firebase Admin Auth not available for user lookup")
      return { exists: false }
    }

    const userRecord = await adminAuth.getUserByEmail(email)
    return {
      exists: true,
      userData: {
        uid: userRecord.uid,
        displayName: userRecord.displayName,
        email: userRecord.email,
      },
    }
  } catch (error) {
    // User not found or other error - this is expected for new users
    console.log("User not found in Firebase Auth:", email)
    return { exists: false }
  }
}

/**
 * Send an invitation email using a template
 */
export const sendInvitationEmail = async (
  invitation: Invitation,
  invitationLink: string,
  templateId?: number,
  additionalParams?: {
    memberCount?: number
    invitationDate?: string
  }
) => {
  console.log("sendInvitationEmail called with:", {
    inviteeEmail: invitation.inviteeEmail,
    squadName: invitation.squadName,
    templateId,
    additionalParams,
  })

  // Check if Brevo API key is configured
  if (!apiKey) {
    console.error("BREVO_API_KEY is not configured")
    return {
      success: false,
      error: "Error sending email, please try again later",
    }
  }

  // Check if the invitee is already a user in the app
  let userCheck: { exists: boolean; userData?: any }
  try {
    userCheck = await checkUserExistsByEmail(invitation.inviteeEmail)
  } catch (error) {
    console.error("Error checking user existence:", error)
    // Default to new user template if user check fails
    userCheck = { exists: false }
  }

  // Determine which template to use
  let useTemplateId: number | undefined
  let templateParams: Record<string, any>

  if (userCheck.exists) {
    // User exists - use regular invitation template with username
    useTemplateId = templateId || EmailTemplates.INVITATION
    templateParams = {
      squadName: invitation.squadName,
      inviterName: invitation.inviterName,
      username: userCheck.userData?.displayName || invitation.inviteeEmail.split("@")[0],
      invitationLink: invitationLink,
      memberCount: additionalParams?.memberCount || 1,
      invitationDate: additionalParams?.invitationDate || new Date().toLocaleDateString(),
    }
  } else {
    // User doesn't exist - use new user invitation template
    useTemplateId = EmailTemplates.INVITATION_NEW_USER
    templateParams = {
      squadName: invitation.squadName,
      inviterName: invitation.inviterName,
      invitationLink: invitationLink,
      invitationDate: additionalParams?.invitationDate || new Date().toLocaleDateString(),
    }
  }

  // Log template ID for debugging
  console.log(
    "Using template ID:",
    useTemplateId,
    "for",
    userCheck.exists ? "existing user" : "new user"
  )

  // Check if template ID is available
  if (!useTemplateId) {
    console.error("No Brevo template ID configured for invitations")
    return {
      success: false,
      error: "Error sending email, please try again later",
    }
  }

  // Send email using Brevo template (no subject needed as it's defined in template)
  console.log("Sending email with params:", {
    to: invitation.inviteeEmail,
    templateId: useTemplateId,
    params: templateParams,
  })

  try {
    const result = await sendEmail({
      to: invitation.inviteeEmail,
      templateId: useTemplateId,
      params: templateParams,
    })

    console.log("Email send result:", result)
    return result
  } catch (error) {
    console.error("Error in sendEmail call:", error)
    return {
      success: false,
      error: `Email sending failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    }
  }
}
