/**
 * Email template configuration
 *
 * This file contains the template IDs for emails sent via Brevo.
 * Update these IDs with your actual template IDs from the Brevo platform.
 */

export const EmailTemplates = {
  // Template for squad invitations
  INVITATION: process.env.BREVO_INVITATION_TEMPLATE_ID
    ? Number(process.env.BREVO_INVITATION_TEMPLATE_ID)
    : 166,

  INVITATION_NEW_USER: process.env.BREVO_INVITATION_NEW_USER_TEMPLATE_ID
    ? Number(process.env.BREVO_INVITATION_NEW_USER_TEMPLATE_ID)
    : 169,

  TRIP_STARTED: process.env.BREVO_TRIP_STARTED_TEMPLATE_ID
    ? Number(process.env.BREVO_TRIP_STARTED_TEMPLATE_ID)
    : 167,

  TRIP_COMPLETED: process.env.BREVO_TRIP_COMPLETED_TEMPLATE_ID
    ? Number(process.env.BREVO_TRIP_COMPLETED_TEMPLATE_ID)
    : 168,

  // Template for welcome emails
  WELCOME: process.env.BREVO_WELCOME_TEMPLATE_ID
    ? Number(process.env.BREVO_WELCOME_TEMPLATE_ID)
    : undefined,

  // Template for password reset
  PASSWORD_RESET: process.env.BREVO_PASSWORD_RESET_TEMPLATE_ID
    ? Number(process.env.BREVO_PASSWORD_RESET_TEMPLATE_ID)
    : undefined,
}
