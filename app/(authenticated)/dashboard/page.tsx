"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { DashboardSkeleton } from "./components/DashboardSkeleton"
import { SquadsTab } from "./components/SquadsTab"
import { UpcomingTripsTab } from "./components/UpcomingTripsTab"
import { PastTripsTab } from "./components/PastTripsTab"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAllTrips } from "@/lib/domains/trip/trip.realtime.hooks"
import { useRealtimeTripsAttendeesDetails } from "@/lib/domains/user-trip/user-trip.realtime.hooks"
import { useMemo } from "react"

export default function DashboardPage() {
  const { user, loading: authLoading } = useAuthStatus()

  // Get squads with real-time updates
  const { squads, loading: squadsLoading } = useRealtimeUserSquads()

  // Get all trips with real-time updates
  const { upcomingTrips, pastTrips, loading: tripsLoading } = useRealtimeUserAllTrips()

  // Get trip IDs for attendee details
  const upcomingTripIds = useMemo(() => upcomingTrips.map((trip) => trip.id), [upcomingTrips])

  // Get attendee details for upcoming trips
  const { tripsAttendeesDetails, loading: attendeesLoading } =
    useRealtimeTripsAttendeesDetails(upcomingTripIds)

  // Determine overall loading state
  const isLoading = authLoading || squadsLoading || tripsLoading

  if (isLoading && !squads.length && !upcomingTrips.length && !pastTrips.length) {
    return <DashboardSkeleton />
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">Welcome back, {user?.displayName || "Friend"}!</p>
      </div>

      <Tabs defaultValue="trips" className="space-y-4">
        <TabsList className="w-full">
          <TabsTrigger value="trips" className="flex-1 md:flex-initial">
            Upcoming Trips
          </TabsTrigger>
          <TabsTrigger value="squads" className="flex-1 md:flex-initial">
            My Squads
          </TabsTrigger>

          <TabsTrigger value="past" className="flex-1 md:flex-initial">
            Past Trips
          </TabsTrigger>
        </TabsList>

        <TabsContent value="squads">
          <SquadsTab squads={squads} upcomingTrips={upcomingTrips} loading={isLoading} />
        </TabsContent>

        <TabsContent value="trips">
          <UpcomingTripsTab
            squads={squads}
            upcomingTrips={upcomingTrips}
            tripsAttendeesDetails={tripsAttendeesDetails}
            loading={isLoading}
          />
        </TabsContent>

        <TabsContent value="past">
          <PastTripsTab squads={squads} pastTrips={pastTrips} loading={isLoading} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
