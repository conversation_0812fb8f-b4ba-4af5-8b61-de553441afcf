"use client"

import { useState, useEffect, Suspense } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeSquad } from "@/lib/domains/squad/squad.realtime.hooks"
import { useIsSquadLeader, useIsSquadMember } from "@/lib/domains/squad/squad.hooks"

// Import our components
import { SquadLoading } from "./components/loading"
import { SquadNotFound } from "./components/not-found"
import { SquadHeader } from "./components/shared/squad-header"
import { InviteDialog } from "./components/invitations/invite-dialog"
import { OverviewTab } from "./components/overview/overview-tab"
import { TripsTab } from "./components/trips/trips-tab"
import { MembersTab } from "./components/members/members-tab"
import { SettingsTab } from "./components/settings/settings-tab"
import { InvitationsTab } from "./components/invitations/invitations-tab"

function SquadPageContent() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const { user, loading: authLoading } = useAuthStatus()
  const squadId = params.id as string

  // Track the active tab for any future tab-specific logic and analytics
  const [activeTab, setActiveTab] = useState("overview")
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false)

  // Handle tab parameter from URL
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Get squad data with real-time updates
  const { squad, loading: squadLoading, error: squadError } = useRealtimeSquad(squadId)

  // Check if user is squad leader or member
  const { isLeader, loading: leaderCheckLoading } = useIsSquadLeader(squadId)
  const { isMember, loading: memberCheckLoading } = useIsSquadMember(squadId)

  // // Handle squad not found or access denied
  // useEffect(() => {
  //   if (squadError || !squad) {
  //     toast({
  //       title: "Squad not found",
  //       description: "The squad you're looking for doesn't exist or you don't have access.",
  //       variant: "destructive",
  //     })
  //     // router.push("/dashboard")
  //   } else if (!isMember && !isLeader) {
  //     toast({
  //       title: "Access denied",
  //       description: "You don't have access to this squad.",
  //       variant: "destructive",
  //     })
  //     router.push("/dashboard")
  //   }
  // }, [toast, router])

  if (authLoading || squadLoading || leaderCheckLoading || memberCheckLoading) {
    return <SquadLoading />
  }

  if (!user) {
    router.push("/login")
    return null
  }

  if (!squad || (!isMember && !isLeader)) {
    return <SquadNotFound />
  }

  return (
    <div className="p-6 max-w-[100vw] overflow-x-hidden">
      <SquadHeader
        squad={squad}
        onInviteClick={() => setInviteDialogOpen(true)}
        isSquadLead={isLeader}
        onRedirectToInvitations={() => {
          setActiveTab("invitations")
          router.push(`/squads/${squad.id}?tab=invitations`)
        }}
      />

      <Tabs value={activeTab} className="space-y-4" onValueChange={setActiveTab}>
        <div className="overflow-x-auto pb-2 max-w-[100vw] no-scrollbar">
          <TabsList className="w-full flex flex-nowrap overflow-x-auto">
            <TabsTrigger value="overview" className="flex-shrink-0">
              Overview
            </TabsTrigger>
            <TabsTrigger value="trips" className="flex-shrink-0">
              Trips
            </TabsTrigger>
            <TabsTrigger value="members" className="flex-shrink-0">
              Members
            </TabsTrigger>
            {isLeader && (
              <TabsTrigger value="invitations" className="flex-shrink-0">
                Invitations
              </TabsTrigger>
            )}
            <TabsTrigger value="settings" className="flex-shrink-0">
              Settings
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview">
          <OverviewTab squad={squad} />
        </TabsContent>

        <TabsContent value="trips">
          <TripsTab squad={squad} />
        </TabsContent>

        <TabsContent value="members">
          <MembersTab squad={squad} onInviteClick={() => setInviteDialogOpen(true)} />
        </TabsContent>

        <TabsContent value="invitations">
          <InvitationsTab squad={squad} onInviteClick={() => setInviteDialogOpen(true)} />
        </TabsContent>

        <TabsContent value="settings">
          <SettingsTab squad={squad} />
        </TabsContent>
      </Tabs>

      {/* Invite Dialog */}
      {squad && (
        <InviteDialog
          open={inviteDialogOpen}
          onOpenChange={setInviteDialogOpen}
          squadId={squad.id}
          squadName={squad.name}
          onInviteSent={() => {
            // Refresh any data if needed
          }}
          onRedirectToInvitations={() => {
            setActiveTab("invitations")
            router.push(`/squads/${squad.id}?tab=invitations`)
          }}
        />
      )}
    </div>
  )
}

export default function SquadPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SquadPageContent />
    </Suspense>
  )
}
