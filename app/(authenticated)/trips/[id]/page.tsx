"use client"

import { useEffect, useState, useRef, use<PERSON>emo, Suspense } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { PageLoading } from "@/components/page-loading"

// Import domain hooks
import { useRealtimeTrip, useIsTripLeader } from "@/lib/domains/trip/trip.hooks"
import { useRealtimeTripTasks } from "@/lib/domains/task/task.realtime.hooks"
import {
  useRealtimeUserTripStatus,
  useRealtimeTripAttendeesWithDetails,
} from "@/lib/domains/user-trip/user-trip.realtime.hooks"
import { useRealtimeSquad, useRealtimeSquadMembers } from "@/lib/domains/squad/squad.realtime.hooks"

// Import our components
import { <PERSON>Header } from "./components/shared/header"
import { TripOverviewTab } from "./components/overview/overview-tab"
import { TasksTab } from "./components/tasks/tasks-tab"
import { TripItineraryTab } from "./components/itinerary/itinerary-tab"
import { TripExpensesTab } from "./components/expenses/expenses-tab"
import { AttendeesTab } from "./components/attendees/attendees-tab"
import { SettingsTab } from "./components/settings/settings-tab"
import { ChatTab } from "./components/chat/chat-tab"
import { AttendanceToggleButton } from "./components/shared/attendance-toggle-button"
import { FloatingAIButton } from "@/components/floating-ai-button"

function TripPageContent() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const { user, loading: authLoading } = useAuthStatus()
  const tripId = params.id as string

  // Get tab from URL query parameter or default to "overview"
  const tabParam = searchParams.get("tab")
  const validTabs = ["overview", "tasks", "itinerary", "expenses", "attendees", "chat", "settings"]

  // State for active tab when user is attending
  const [activeTab, setActiveTab] = useState(
    validTabs.includes(tabParam || "") ? tabParam : "overview"
  )
  const tabsListRef = useRef<HTMLDivElement>(null)
  const [isTripOngoing, setIsTripOngoing] = useState<boolean>(false)

  // Function to handle tab changes with URL updates
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    // Update URL without refreshing the page
    const url = new URL(window.location.href)
    url.searchParams.set("tab", value)
    window.history.pushState({}, "", url.toString())
  }

  // Get trip data with real-time updates
  const { trip, loading: tripLoading, error: tripError } = useRealtimeTrip(tripId)

  // Check if user is trip leader
  const { isLeader, loading: leaderCheckLoading } = useIsTripLeader(tripId)

  // Get trip tasks with real-time updates (only if on tasks tab)
  const {
    tasks,
    loading: tasksLoading,
    error: tasksError,
  } = useRealtimeTripTasks(activeTab === "tasks" ? tripId : "")

  // Get user's trip status with real-time updates
  const { status, loading: statusLoading, error: statusError } = useRealtimeUserTripStatus(tripId)

  // Get trip attendees with user details in a single real-time update
  const {
    attendeesWithDetails,
    loading: attendeesLoading,
    error: attendeesError,
  } = useRealtimeTripAttendeesWithDetails(tripId)

  // Extract the separate arrays for backward compatibility with components (memoized to prevent re-renders)
  const attendees = useMemo(
    () => attendeesWithDetails.map((a) => ({ ...a })),
    [attendeesWithDetails]
  )
  const attendeesDetails = useMemo(
    () => attendeesWithDetails.map((a) => a.user),
    [attendeesWithDetails]
  )

  // Create chat-compatible attendees mapping
  const chatAttendees = useMemo(
    () =>
      attendeesWithDetails
        .filter((a) => a.user?.uid) // Filter out any attendees without valid UIDs
        .map((a) => ({
          id: a.user.uid, // Map uid to id for chat compatibility
          displayName: a.user.displayName || a.user.email || "Unknown User",
          email: a.user.email,
          photoURL: a.user.photoURL,
        })),
    [attendeesWithDetails]
  )

  // Get squad data with real-time updates (only when needed)
  const { squad, loading: squadLoading, error: squadError } = useRealtimeSquad(trip?.squadId || "")

  // Get squad members with real-time updates (only when needed for attendees tab)
  const { members: squadMembers } = useRealtimeSquadMembers(
    activeTab === "attendees" && trip?.squadId ? trip.squadId : ""
  )

  // Extract squad member user IDs for the attendees tab
  const squadMemberIds = useMemo(
    () => squadMembers?.map((member) => member.userId) || [],
    [squadMembers]
  )

  // Check if user has restricted access (for active trips, only going users get full access)
  const hasRestrictedAccess = trip?.status === "active" && (!status || status !== "going")

  // Available tabs based on user access
  const availableTabs = hasRestrictedAccess
    ? ["overview"] // Only overview for non-going users on active trips
    : ["overview", "tasks", "itinerary", "expenses", "attendees", "chat"] // Full access for others

  // Effect to update active tab when URL changes
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab && validTabs.includes(tab) && tab !== activeTab) {
      // Prevent non-leaders from accessing settings tab
      if (tab === "settings" && !isLeader) {
        setActiveTab("overview")
        // Update URL to remove invalid tab
        const url = new URL(window.location.href)
        url.searchParams.set("tab", "overview")
        window.history.replaceState({}, "", url.toString())
      }
      // Prevent restricted users from accessing non-overview tabs
      else if (hasRestrictedAccess && !availableTabs.includes(tab)) {
        setActiveTab("overview")
        // Update URL to remove invalid tab
        const url = new URL(window.location.href)
        url.searchParams.set("tab", "overview")
        window.history.replaceState({}, "", url.toString())
      } else {
        setActiveTab(tab)
      }
    }
  }, [searchParams, validTabs, activeTab, isLeader, hasRestrictedAccess, availableTabs])

  // Effect to scroll active tab into view on mobile
  useEffect(() => {
    if (tabsListRef.current && activeTab) {
      setTimeout(() => {
        const tabElement = tabsListRef.current?.querySelector(`[data-value="${activeTab}"]`)
        if (tabElement && tabsListRef.current) {
          // Scroll the tab into view with some padding
          const container = tabsListRef.current
          const scrollLeft =
            tabElement.getBoundingClientRect().left -
            container.getBoundingClientRect().left +
            container.scrollLeft -
            16 // Add some padding

          container.scrollTo({
            left: scrollLeft,
            behavior: "smooth",
          })
        }
      }, 100) // Small delay to ensure the DOM is updated
    }
  }, [activeTab])

  // Check if trip is ongoing
  useEffect(() => {
    if (trip?.startDate && trip?.endDate) {
      const now = new Date()
      const tripStartDate = trip.startDate.toDate()
      const tripEndDate = trip.endDate.toDate()
      setIsTripOngoing(now >= tripStartDate && now <= tripEndDate)
    }
  }, [trip])

  // Handle trip not found or error
  useEffect(() => {
    if (!tripLoading && tripError) {
      toast({
        title: "Error",
        description: "Trip not found or could not be loaded",
        variant: "destructive",
      })
      router.push("/trips")
    }
  }, [tripLoading, tripError, toast, router])

  // Determine if user is attending the trip
  const userAttendingTrip = status === "going"

  // Calculate loading state
  const loading = authLoading || tripLoading
  // Handle floating AI button click
  const handleFloatingAIClick = () => {
    if (activeTab === "tasks") {
      // Scroll to AI Task Suggestions section
      const taskSuggestionsElement = document.getElementById("ai-task-suggestions")
      if (taskSuggestionsElement) {
        taskSuggestionsElement.scrollIntoView({ behavior: "smooth", block: "start" })
      }
    } else if (activeTab === "itinerary") {
      // Scroll to AI Activities Suggestions section
      const itinerarySuggestionsElement = document.getElementById("ai-activities-suggestions")
      if (itinerarySuggestionsElement) {
        itinerarySuggestionsElement.scrollIntoView({ behavior: "smooth", block: "start" })
      }
    }
  }

  // Show floating button only on tasks and itinerary tabs when user is attending
  const showFloatingButton =
    userAttendingTrip && (activeTab === "tasks" || activeTab === "itinerary")

  if (loading || !trip) {
    return <PageLoading message="Loading trip details..." />
  }

  // Get pending tasks for overview
  const pendingTasks = tasks?.filter((task) => !task.completed).slice(0, 3) || []
  const squadName = squad?.name || "Squad"

  return (
    <div className="p-6 max-w-[100vw] overflow-x-hidden">
      <TripHeader trip={trip} attendees={attendees} squadName={squadName} />

      <div className="bg-muted/30 p-2 px-3 mb-4 rounded-lg border shadow-sm">
        <AttendanceToggleButton tripId={tripId} status={status} tripStatus={trip.status} />
      </div>

      {!userAttendingTrip ? (
        // Only show overview tab if user is not attending
        <div className="space-y-4 md:space-y-6">
          <div className="space-y-2 md:space-y-4 md:mb-2">
            <div className="flex justify-between items-center">
              <h2 className="text-lg md:text-xl font-semibold">Trip Overview</h2>
            </div>
          </div>
          <TripOverviewTab
            trip={trip}
            attendees={attendees}
            attendeesDetails={attendeesDetails}
            pendingTasks={pendingTasks}
            isLeader={isLeader}
            squadName={squadName}
            userAttendingTrip={userAttendingTrip}
            isActive={true}
          />
        </div>
      ) : (
        // Show all tabs if user is attending
        <Tabs
          defaultValue="overview"
          className="space-y-4 w-full"
          onValueChange={handleTabChange}
          value={activeTab || "overview"}
        >
          <div className="flex flex-col space-y-4">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center w-full gap-4">
              <div className="overflow-x-auto pb-2 max-w-[100vw] no-scrollbar">
                <TabsList className="w-full flex flex-nowrap overflow-x-auto" ref={tabsListRef}>
                  <TabsTrigger value="overview" className="flex-shrink-0">
                    Overview
                  </TabsTrigger>
                  {availableTabs.includes("tasks") && (
                    <TabsTrigger value="tasks" className="flex-shrink-0">
                      Tasks
                    </TabsTrigger>
                  )}
                  {availableTabs.includes("itinerary") && (
                    <TabsTrigger value="itinerary" className="flex-shrink-0">
                      Itinerary
                    </TabsTrigger>
                  )}
                  {availableTabs.includes("chat") && (
                    <TabsTrigger value="chat" className="flex-shrink-0">
                      Chat
                    </TabsTrigger>
                  )}
                  {availableTabs.includes("expenses") && (
                    <TabsTrigger value="expenses" className="flex-shrink-0">
                      Expenses
                    </TabsTrigger>
                  )}
                  {availableTabs.includes("attendees") && (
                    <TabsTrigger value="attendees" className="flex-shrink-0">
                      Attendees
                    </TabsTrigger>
                  )}

                  {isLeader && (
                    <TabsTrigger value="settings" className="flex-shrink-0">
                      Settings
                    </TabsTrigger>
                  )}
                </TabsList>
              </div>
            </div>
          </div>

          <TabsContent value="overview" className="space-y-6 w-full max-w-full overflow-x-hidden">
            {hasRestrictedAccess && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 text-blue-800">
                  <span className="text-sm font-medium">ℹ️ Limited Access</span>
                </div>
                <p className="text-blue-700 text-sm mt-1">
                  This trip is currently active. Since you're not marked as "going", you can only
                  view the trip overview. To access all trip features, you would have needed to
                  confirm your attendance before the trip started.
                </p>
              </div>
            )}
            <TripOverviewTab
              trip={trip}
              attendees={attendees}
              attendeesDetails={attendeesDetails}
              pendingTasks={pendingTasks}
              isLeader={isLeader}
              squadName={squadName}
              userAttendingTrip={userAttendingTrip}
              isActive={activeTab === "overview"}
            />
          </TabsContent>

          {availableTabs.includes("tasks") && (
            <TabsContent value="tasks" className="space-y-4 w-full max-w-full overflow-x-hidden">
              <TasksTab
                trip={trip}
                tasks={tasks}
                currentUserId={user?.uid || ""}
                attendeesWithDetails={attendeesWithDetails}
                isActive={activeTab === "tasks"}
              />
            </TabsContent>
          )}

          {availableTabs.includes("itinerary") && (
            <TabsContent
              value="itinerary"
              className="space-y-4 w-full max-w-full overflow-x-hidden"
            >
              <TripItineraryTab trip={trip} isActive={activeTab === "itinerary"} />
            </TabsContent>
          )}

          {availableTabs.includes("expenses") && (
            <TabsContent value="expenses" className="space-y-4 w-full max-w-full overflow-x-hidden">
              <TripExpensesTab trip={trip} attendees={attendees} />
            </TabsContent>
          )}

          {availableTabs.includes("attendees") && (
            <TabsContent
              value="attendees"
              className="space-y-4 w-full max-w-full overflow-x-hidden"
            >
              <AttendeesTab
                trip={trip}
                attendees={attendeesWithDetails}
                squadMembers={squadMemberIds}
                currentUserId={user?.uid || ""}
                isActive={activeTab === "attendees"}
              />
            </TabsContent>
          )}

          {availableTabs.includes("chat") && (
            <TabsContent value="chat" className="space-y-4 w-full max-w-full overflow-x-hidden">
              <ChatTab trip={trip} attendees={chatAttendees} isActive={activeTab === "chat"} />
            </TabsContent>
          )}

          {isLeader && (
            <TabsContent value="settings" className="space-y-4 w-full max-w-full overflow-x-hidden">
              <SettingsTab trip={trip} />
            </TabsContent>
          )}
        </Tabs>
      )}

      {/* Floating AI Suggestions Button - positioned relative to viewport */}
      {showFloatingButton && <FloatingAIButton onClick={handleFloatingAIClick} />}
    </div>
  )
}

export default function TripPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TripPageContent />
    </Suspense>
  )
}
