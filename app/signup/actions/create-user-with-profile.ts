"use server"

import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth"
import { auth, db } from "@/lib/firebase"
import { doc, setDoc, serverTimestamp } from "firebase/firestore"
import { uploadProfilePictureAction } from "./upload-profile-picture"
import { ReferralAdminService } from "@/lib/domains/referral/referral.admin.service"

export interface CreateUserWithProfileData {
  email: string
  password: string
  name: string
  bio?: string
  location?: string
  locationPlaceId?: string
  selectedTravelPreferences: string[]
  budget: string
  selectedAvailability: string[]
  selectedMonths: string[]
  selectedTravelGroups: string[]
  referralCode?: string // Optional referral code
}

export interface CreateUserWithProfileResult {
  success: boolean
  error?: string
  redirectUrl?: string
}

export async function createUserWithProfileAction(
  userData: CreateUserWithProfileData,
  profilePictureFormData?: FormData
): Promise<CreateUserWithProfileResult> {
  console.log("Create User With Profile Action:", {
    email: userData.email,
    hasProfilePicture: !!profilePictureFormData?.get("file"),
  })

  try {
    // Validate required fields
    if (!userData.email || !userData.password || !userData.name) {
      return {
        success: false,
        error: "Missing required fields: email, password, and name are required",
      }
    }

    let profilePictureURL: string | null = null

    // Upload profile picture first if provided
    if (profilePictureFormData?.get("file")) {
      console.log("Uploading profile picture...")
      const uploadResult = await uploadProfilePictureAction(profilePictureFormData)

      if (!uploadResult.success) {
        return {
          success: false,
          error: `Profile picture upload failed: ${uploadResult.error}`,
        }
      }

      profilePictureURL = uploadResult.url || null
      console.log("Profile picture uploaded successfully:", profilePictureURL)
    }

    // Create user account with Firebase Authentication
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      userData.email,
      userData.password
    )

    const userId = userCredential.user.uid
    console.log("User created with ID:", userId)

    // Update the user profile with the display name and photo URL
    await updateProfile(userCredential.user, {
      displayName: userData.name,
      photoURL: profilePictureURL,
    })

    // 1. Save user document to Firestore
    const userRef = doc(db, "users", userId)
    await setDoc(userRef, {
      uid: userId,
      email: userData.email,
      displayName: userData.name,
      photoURL: profilePictureURL,
      bio: userData.bio || "",
      location: userData.location || null,
      locationPlaceId: userData.locationPlaceId || null,
      createdAt: serverTimestamp(),
      travelPreferences: userData.selectedTravelPreferences,
      budgetRange: userData.budget,
      availabilityPreferences: userData.selectedAvailability,
      preferredTravelSeasons: userData.selectedMonths,
      travelGroupPreferences: userData.selectedTravelGroups,
      newUser: true,
      firstLogin: serverTimestamp(),
    })

    // 2. Initialize user preferences document
    const preferencesRef = doc(db, "userPreferences", userId)
    await setDoc(preferencesRef, {
      userId,
      theme: "system",
      location: userData.location || null,
      locationPlaceId: userData.locationPlaceId || null,
      travelPreferences: userData.selectedTravelPreferences,
      budgetRange:
        userData.budget === "budget-friendly"
          ? [0, 500]
          : userData.budget === "mid-range"
            ? [500, 2000]
            : [2000, 10000],
      availabilityPreferences: userData.selectedAvailability,
      preferredTravelSeasons: userData.selectedMonths,
      travelGroupPreferences: userData.selectedTravelGroups,
      aiEnabled: true,
      proactiveSuggestions: true,
      notificationsEnabled: true,
      emailNotifications: true,
      pushNotifications: true,
      tripUpdatesNotifications: true,
      squadMessagesNotifications: true,
      invitationNotifications: true,
      aiSuggestionsNotifications: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    })

    // 3. Initialize user AI usage document
    const aiUsageRef = doc(db, "userAiUsage", userId)
    await setDoc(aiUsageRef, {
      userId,
      aiUsageToday: 0,
      aiUsageThisWeek: 0,
      aiUsageLastReset: serverTimestamp(),
      aiUsageWeekStart: serverTimestamp(),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    })

    // 4. CRITICAL FIX: Create free subscription entry for new user
    try {
      const { FlatSubscriptionService } = await import(
        "@/lib/domains/user-subscription/flat-subscription.service"
      )
      const freeSubscriptionResult = await FlatSubscriptionService.addFreeSubscription(userId)
      if (freeSubscriptionResult.success) {
        console.log("Free subscription created for new user:", userId)
      } else {
        console.error(
          "Failed to create free subscription for new user:",
          freeSubscriptionResult.error
        )
        // Don't fail signup if subscription creation fails, but log it
      }
    } catch (error) {
      console.error("Error creating free subscription for new user:", error)
      // Don't fail signup if subscription creation fails
    }

    // 5. Generate referral code for the new user
    try {
      const newUserReferralCode = await ReferralAdminService.generateReferralCode()
      await ReferralAdminService.createReferralCode(newUserReferralCode, userId)
      console.log("Referral code created for new user:", newUserReferralCode)
    } catch (error) {
      console.error("Error creating referral code for new user:", error)
      // Don't fail the signup process if referral code creation fails
    }

    // 6. Process referral code if provided
    if (userData.referralCode) {
      console.log("🎯 Processing referral code:", userData.referralCode, "for user:", userId)
      try {
        const referralResult = await ReferralAdminService.processReferral(
          userData.referralCode,
          userId,
          userData.email
        )

        console.log("🔄 Referral processing result:", referralResult)

        if (referralResult.success) {
          console.log("✅ Referral processed successfully:", referralResult)

          // Check for newly unlocked perks for the referrer
          if (referralResult.perksUnlocked && referralResult.perksUnlocked.length > 0) {
            console.log("🎁 Perks unlocked for referrer:", referralResult.perksUnlocked)
          }
        } else {
          console.error("❌ Failed to process referral:", referralResult.error)
        }
      } catch (error) {
        console.error("💥 Error processing referral code:", error)
        // Don't fail the signup process if referral processing fails
      }
    } else {
      console.log("ℹ️ No referral code provided during signup")
    }

    console.log("User documents created successfully")

    return {
      success: true,
      redirectUrl: "/login?message=account_created",
    }
  } catch (error: any) {
    console.error("Create user with profile error:", error)

    // Handle specific Firebase auth errors
    if (error.code === "auth/email-already-in-use") {
      return {
        success: false,
        error: "This email address is already registered. Please log in or use a different email.",
      }
    } else if (error.code === "auth/weak-password") {
      return {
        success: false,
        error: "Password is too weak. Please choose a stronger password.",
      }
    } else if (error.code === "auth/invalid-email") {
      return {
        success: false,
        error: "Invalid email address format.",
      }
    } else {
      return {
        success: false,
        error: error.message || "Something went wrong. Please try again.",
      }
    }
  }
}
